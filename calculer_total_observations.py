#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Script pour calculer le total des observations dans le tableau granulaire
"""

import re

def calculer_total_observations():
    """
    Calcule le total des observations du tableau granulaire
    """
    nom_fichier = "analyse_granulaire_diff_20250629_234319.txt"
    
    total_observations = 0
    total_s = 0
    total_o = 0
    lignes_avec_donnees = 0
    
    print("🔍 ANALYSE DU TABLEAU GRANULAIRE")
    print("=" * 50)
    
    try:
        with open(nom_fichier, 'r', encoding='utf-8') as f:
            contenu = f.read()
        
        # Pattern pour extraire les lignes du tableau granulaire
        pattern = r'DIFF_(\d+\.\d+) \|\s*(\d+) obs \| S:\s*(\d+) \| O:\s*(\d+) \|'
        matches = re.findall(pattern, contenu)
        
        print(f"📊 Nombre de lignes trouvées: {len(matches)}")
        print()
        
        for match in matches:
            diff_val = float(match[0])
            obs = int(match[1])
            s_count = int(match[2])
            o_count = int(match[3])
            
            total_observations += obs
            total_s += s_count
            total_o += o_count
            
            if obs > 0:
                lignes_avec_donnees += 1
        
        print("📈 RÉSULTATS:")
        print(f"   • Total observations: {total_observations:,}")
        print(f"   • Total S: {total_s:,}")
        print(f"   • Total O: {total_o:,}")
        print(f"   • Lignes avec données: {lignes_avec_donnees}")
        print(f"   • Lignes sans données: {len(matches) - lignes_avec_donnees}")
        print()
        
        # Vérification de cohérence
        print("🔍 VÉRIFICATION DE COHÉRENCE:")
        print(f"   • S + O = {total_s + total_o:,}")
        print(f"   • Total observations = {total_observations:,}")
        
        if total_s + total_o == total_observations:
            print("   ✅ COHÉRENCE PARFAITE: S + O = Total observations")
        else:
            print("   ❌ INCOHÉRENCE DÉTECTÉE!")
        
        print()
        print("📊 COMPARAISON AVEC LES TOTAUX ANNONCÉS:")
        print(f"   • PATTERNS S annoncé: 272,316")
        print(f"   • PATTERNS O annoncé: 271,606")
        print(f"   • Total annoncé: 543,922")
        print()
        print(f"   • S calculé: {total_s:,}")
        print(f"   • O calculé: {total_o:,}")
        print(f"   • Total calculé: {total_observations:,}")
        
        # Vérification finale
        total_annonce = 272316 + 271606
        if total_observations == total_annonce:
            print("   ✅ PARFAITE CORRESPONDANCE!")
        else:
            difference = abs(total_observations - total_annonce)
            print(f"   ⚠️  DIFFÉRENCE: {difference:,} observations")
        
        return total_observations, total_s, total_o
        
    except Exception as e:
        print(f"❌ Erreur: {e}")
        return None, None, None

if __name__ == "__main__":
    calculer_total_observations()
