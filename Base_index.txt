- Voici toutes les valeurs présentes dans l'index 5 

0_A_BANKER	1_A_BANKER
0_B_BANKER	1_B_BANKER
0_C_BANKER	1_C_BANKER

0_A_PLAYER	1_A_PLAYER
0_B_PLAYER	1_B_PLAYER
0_C_PLAYER	1_C_PLAYER

0_A_TIE		1_A_TIE
0_B_TIE		1_B_TIE
0_C_TIE		1_C_TIE

Index 1 : 0, 1 
Index 2 : A, B, C
Index 3 : BANKER, PLAYER, TIE

- Nous savons que : 
Si index1 = 0 à la main n avec index2 = C à la main n, alors à la main n+1 : index1 sera égal à 1
Si index1 = 1 à la main n avec index2 = C à la main n, alors à la main n+1 : index1 sera égal à 0

Si index1 = 0 à la main n avec index2 = A à la main n, alors à la main n+1 : index1 sera égal à 0
Si index1 = 1 à la main n avec index2 = A à la main n, alors à la main n+1 : index1 sera égal à 1

Si index1 = 0 à la main n avec index2 = B à la main n, alors à la main n+1 : index1 sera égal à 0
Si index1 = 1 à la main n avec index2 = B à la main n, alors à la main n+1 : index1 sera égal à 1

- Nous avons comme base pour comprendre chaque partie : histoire.txt

Le tableau d'une partie est par exemple, comme ceci : 

PARTIE 1
BURN: 4 cartes | Première carte: 3♦ | INDEX1 initial: 0
----------------------------------------------------------------------
Main | Manche | INDEX1 | INDEX2   | INDEX3 | INDEX5
-------------------------------------------------------
   1 | 1      |      0 | C        | TIE    | 0_C_TIE
   2 | 1      |      1 | A        | BANKER | 1_A_BANKER
   3 | 2      |      1 | C        | PLAYER | 1_C_PLAYER
   4 | 3      |      0 | B        | PLAYER | 0_B_PLAYER
   5 | 4      |      0 | C        | BANKER | 0_C_BANKER
   6 | 5      |      1 | C        | PLAYER | 1_C_PLAYER
   7 | 6      |      0 | A        | BANKER | 0_A_BANKER
   8 | 7      |      0 | C        | BANKER | 0_C_BANKER
   9 | 8      |      1 | C        | BANKER | 1_C_BANKER
  10 | 9      |      0 | C        | PLAYER | 0_C_PLAYER
  11 | 10     |      1 | B        | PLAYER | 1_B_PLAYER
  12 | 11     |      1 | B        | BANKER | 1_B_BANKER
  13 | 12     |      1 | A        | PLAYER | 1_A_PLAYER
  14 | 13     |      1 | B        | BANKER | 1_B_BANKER
  15 | 14     |      1 | A        | PLAYER | 1_A_PLAYER
  16 | 15     |      1 | C        | PLAYER | 1_C_PLAYER
  17 | 16     |      0 | A        | BANKER | 0_A_BANKER
  18 | 17     |      0 | C        | TIE    | 0_C_TIE

- Lors de la main n, nous voulons pouvoir prédire BANKER ou PLAYER (2 valeurs dans l'index 3) pour la main n+1.

En tenant compte de tout ces éléments, comment t'y prendrais tu pour : Lors de la main n,  prédire BANKER ou PLAYER (2 valeurs dans l'index 3) pour la main n+1.

?

