#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Script pour tracer le calcul complet de DIFF depuis le début
"""

import json
import math
from collections import Counter

def calculer_entropie_locale(sequence):
    """
    Calcule l'entropie MÉTRIQUE PAR BLOCS d'une séquence (L4 ou L5)
    Analyse les patterns temporels dans la séquence
    Formule: H_bloc(X) = H(blocs) / longueur_bloc
    """
    if not sequence:
        return 0.0

    longueur_sequence = len(sequence)
    
    # Pour L4 et L5, on analyse par blocs de longueur 2 (patterns de transitions)
    longueur_bloc = min(2, longueur_sequence)
    
    if longueur_sequence < longueur_bloc:
        # Séquence trop courte, utiliser entropie Shannon classique
        counts = Counter(sequence)
        total = len(sequence)
        entropie = 0.0
        for count in counts.values():
            if count > 0:
                p = count / total
                entropie -= p * math.log2(p)
        return entropie

    # Extraire tous les blocs de longueur donnée
    blocs = {}
    for i in range(longueur_sequence - longueur_bloc + 1):
        bloc = tuple(sequence[i:i+longueur_bloc])
        blocs[bloc] = blocs.get(bloc, 0) + 1

    # Calculer les probabilités des blocs
    total_blocs = sum(blocs.values())
    if total_blocs == 0:
        return 0.0

    # Entropie des blocs
    entropie_blocs = 0.0
    for count in blocs.values():
        if count > 0:
            p = count / total_blocs
            entropie_blocs -= p * math.log2(p)

    # Normaliser par la longueur du bloc (entropie par symbole)
    return entropie_blocs / longueur_bloc

def calculer_entropie_globale(sequence):
    """
    Calcule l'entropie MÉTRIQUE PAR BLOCS d'une séquence globale (main 1 à n)
    """
    if not sequence:
        return 0.0

    longueur_sequence = len(sequence)
    
    # Pour séquences longues, on analyse par blocs de longueur 3 (patterns étendus)
    longueur_bloc = min(3, longueur_sequence)
    
    if longueur_sequence < longueur_bloc:
        # Séquence trop courte, utiliser entropie Shannon classique
        counts = Counter(sequence)
        total = len(sequence)
        entropie = 0.0
        for count in counts.values():
            if count > 0:
                p = count / total
                entropie -= p * math.log2(p)
        return entropie

    # Extraire tous les blocs de longueur donnée
    blocs = {}
    for i in range(longueur_sequence - longueur_bloc + 1):
        bloc = tuple(sequence[i:i+longueur_bloc])
        blocs[bloc] = blocs.get(bloc, 0) + 1

    # Calculer les probabilités des blocs
    total_blocs = sum(blocs.values())
    if total_blocs == 0:
        return 0.0

    # Entropie des blocs
    entropie_blocs = 0.0
    for count in blocs.values():
        if count > 0:
            p = count / total_blocs
            entropie_blocs -= p * math.log2(p)

    # Normaliser par la longueur du bloc (entropie par symbole)
    return entropie_blocs / longueur_bloc

def tracer_calcul_diff_exemple():
    """
    Trace le calcul complet de DIFF pour un exemple concret
    """
    print("🔍 TRAÇAGE COMPLET DU CALCUL DE DIFF")
    print("=" * 60)
    
    # Charger le dataset pour un exemple réel
    try:
        with open("dataset_baccarat_lupasco_20250629_165801.json", 'r', encoding='utf-8') as f:
            dataset = json.load(f)
        
        # Prendre la première partie comme exemple
        partie = dataset['parties'][0]
        mains = partie['mains']
        
        print(f"📊 EXEMPLE: Partie 1, {len(mains)} mains")
        print()
        
        # Analyser la main 5 (première main analysable)
        position_main = 4  # Index 4 = main 5
        
        print(f"🎯 ANALYSE MAIN {position_main + 1}")
        print("-" * 40)
        
        # 1. Extraire les séquences L4 et L5
        sequence_4 = []
        for j in range(position_main-3, position_main+1):
            sequence_4.append(mains[j]['index5'])
        
        sequence_5 = []
        for j in range(position_main-4, position_main+1):
            sequence_5.append(mains[j]['index5'])
        
        print(f"📝 SÉQUENCES EXTRAITES:")
        print(f"   L4 (mains {position_main-2} à {position_main+1}): {sequence_4}")
        print(f"   L5 (mains {position_main-3} à {position_main+1}): {sequence_5}")
        print()
        
        # 2. Calculer entropies locales
        entropie_l4 = calculer_entropie_locale(sequence_4)
        entropie_l5 = calculer_entropie_locale(sequence_5)
        
        print(f"🧮 ENTROPIES LOCALES:")
        print(f"   Entropie L4: {entropie_l4:.6f}")
        print(f"   Entropie L5: {entropie_l5:.6f}")
        print()
        
        # 3. Calculer entropie globale
        seq_globale = []
        for j in range(1, position_main+1):  # Main 1 à main position_main
            seq_globale.append(mains[j]['index5'])
        
        entropie_globale = calculer_entropie_globale(seq_globale)
        
        print(f"🌍 ENTROPIE GLOBALE:")
        print(f"   Séquence globale (mains 1 à {position_main+1}): {seq_globale}")
        print(f"   Entropie globale: {entropie_globale:.6f}")
        print()
        
        # 4. Calculer ratios
        if entropie_globale > 0:
            ratio_l4 = entropie_l4 / entropie_globale
            ratio_l5 = entropie_l5 / entropie_globale
        else:
            ratio_l4 = float('inf') if entropie_l4 > 0 else 0.0
            ratio_l5 = float('inf') if entropie_l5 > 0 else 0.0
        
        print(f"📊 RATIOS ENTROPIQUES:")
        print(f"   ratio_L4 = entropie_L4 / entropie_globale = {entropie_l4:.6f} / {entropie_globale:.6f} = {ratio_l4:.6f}")
        print(f"   ratio_L5 = entropie_L5 / entropie_globale = {entropie_l5:.6f} / {entropie_globale:.6f} = {ratio_l5:.6f}")
        print()
        
        # 5. Calculer DIFF final
        diff_main_n = abs(ratio_l4 - ratio_l5)
        
        print(f"🎯 CALCUL FINAL DE DIFF:")
        print(f"   DIFF = |ratio_L4 - ratio_L5|")
        print(f"   DIFF = |{ratio_l4:.6f} - {ratio_l5:.6f}|")
        print(f"   DIFF = |{ratio_l4 - ratio_l5:.6f}|")
        print(f"   DIFF = {diff_main_n:.6f}")
        print()
        
        print("✅ PROCESSUS COMPLET TRACÉ!")
        print()
        print("📋 RÉSUMÉ DU PROCESSUS:")
        print("1. Extraction séquences L4 (4 dernières mains) et L5 (5 dernières mains)")
        print("2. Calcul entropie locale pour L4 et L5 (entropie par blocs de longueur 2)")
        print("3. Calcul entropie globale (main 1 à main n, entropie par blocs de longueur 3)")
        print("4. Calcul ratios: ratio_L4 = entropie_L4 / entropie_globale")
        print("5. Calcul ratios: ratio_L5 = entropie_L5 / entropie_globale")
        print("6. Calcul DIFF final: DIFF = |ratio_L4 - ratio_L5|")
        
        return diff_main_n
        
    except Exception as e:
        print(f"❌ Erreur: {e}")
        return None

if __name__ == "__main__":
    tracer_calcul_diff_exemple()
