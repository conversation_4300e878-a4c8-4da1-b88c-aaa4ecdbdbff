#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
PRÉDICTEUR FRACTAL INDEX5 - EXPLOITATION DES FAILLES MATHÉMATIQUES DU BACCARAT
==============================================================================

Basé sur l'analyse fractale des proportions INDEX5 et l'exploitation des biais
structurels intrinsèques aux règles du baccarat.

Auteur: Système d'IA Augment
Date: 2025-01-02

STRUCTURE DU PROGRAMME:
======================
1. IMPORTS ET CONFIGURATION
2. CONSTANTES ET PARAMÈTRES FRACTALS
3. CLASSE PRINCIPALE - INITIALISATION
4. MÉTHODES D'ANALYSE FRACTALE
5. MÉTHODES DE PRÉDICTION SPÉCIALISÉES
6. MÉTHODES D'ÉVALUATION ET PERFORMANCE
7. MÉTHODES DE RAPPORT ET VISUALISATION
8. EXEMPLE D'UTILISATION ET TESTS
"""

# ============================================================================
# 1. IMPORTS ET CONFIGURATION
# ============================================================================

import numpy as np
import pandas as pd
from scipy import stats
from collections import deque, Counter
import json
import warnings
warnings.filterwarnings('ignore')

# ============================================================================
# 2. CONSTANTES ET PARAMÈTRES FRACTALS
# ============================================================================

# Proportions fractales de référence (invariantes d'échelle)
PROPORTIONS_FRACTALES = {
    'A': 0.3786,  # Naturels - Zone d'équilibre
    'B': 0.3175,  # Double tirage - Avantage PLAYER
    'C': 0.3039   # Tirage simple - Avantage BANKER
}

# Biais structurels exploitables
BIAIS_STRUCTURELS = {
    'B_PLAYER': 0.079,   # +7.9% d'avantage PLAYER
    'C_BANKER': 0.121,   # +12.1% d'avantage BANKER
    'A_EQUILIBRE': 0.000  # Équilibre parfait
}

# Paramètres pour estimation de probabilités conditionnelles
PARAMETRES_PROBABILITES = {
    'ALPHA_LAPLACE': 1.0,        # Paramètre de lissage de Laplace
    'FENETRE_MIN': 50,           # Taille minimale pour estimation fiable
    'CONFIANCE': 0.95,           # Niveau de confiance pour intervalles
    'FACTEUR_OUBLI': 0.95,       # Facteur d'oubli pour adaptation temporelle
    'BOOTSTRAP_SAMPLES': 1000,    # Échantillons pour validation bootstrap
    'SEUIL_SIGNIFICATIVITE': 0.05 # Seuil pour tests statistiques
}

# Règles BCT (déterministes)
REGLES_BCT = {
    'C': 'ALTERNANCE',    # 0→1, 1→0
    'A': 'CONSERVATION',  # 0→0, 1→1
    'B': 'CONSERVATION'   # 0→0, 1→1
}

# Paramètres d'analyse fractale
SEUILS_HURST = {
    'PERSISTANT': 0.6,
    'ANTI_PERSISTANT': 0.4,
    'NEUTRE_MIN': 0.4,
    'NEUTRE_MAX': 0.6
}

# Paramètres de confiance
SEUILS_CONFIANCE = {
    'MINIMUM': 0.3,
    'MAXIMUM': 0.95,
    'CYCLE_BONUS': 0.1,
    'ENTROPIE_SEUIL': 0.5
}

# ============================================================================
# 3. CLASSE PRINCIPALE - INITIALISATION
# ============================================================================

class PredicteurFractalIndex5:
    """
    Prédicteur basé sur l'analyse fractale des patterns INDEX5
    Exploite les failles mathématiques intrinsèques du baccarat

    Architecture:
    - Analyse fractale multi-échelle
    - Détection de cycles temporels
    - Exploitation des biais structurels
    - Fusion de prédictions pondérées
    """

    def __init__(self, fenetre_analyse=100, fenetre_hurst=50):
        """
        Initialisation du prédicteur fractal

        Args:
            fenetre_analyse: Taille de la fenêtre d'analyse principale
            fenetre_hurst: Taille de la fenêtre pour l'exposant de Hurst
        """
        # Paramètres de fenêtrage
        self.fenetre_analyse = fenetre_analyse
        self.fenetre_hurst = fenetre_hurst

        # Constantes fractales (références aux constantes globales)
        self.proportions_fractales = PROPORTIONS_FRACTALES
        self.biais_structurels = BIAIS_STRUCTURELS
        self.regles_bct = REGLES_BCT
        self.parametres_prob = PARAMETRES_PROBABILITES

        # Structures de données
        self.historique = deque(maxlen=1000)
        self.predictions_historique = []
        self.performance = {'correct': 0, 'total': 0}

        # 🔬 NOUVEAU: Système de probabilités conditionnelles adaptatives
        self.compteurs_conditionnels = {}  # Comptage (INDEX1,INDEX2) → INDEX3
        self.probabilites_conditionnelles = {}  # P(INDEX3|INDEX1,INDEX2)
        self.intervalles_confiance = {}  # Intervalles de confiance
        self.historique_probabilites = deque(maxlen=200)  # Historique pour adaptation

        # Cache pour optimisation
        self._cache_hurst = {}
        self._cache_cycles = {}

    def charger_dataset_json(self, chemin_fichier, nb_parties=10):
        """
        Charge un dataset JSON de parties de baccarat

        Args:
            chemin_fichier: Chemin vers le fichier JSON
            nb_parties: Nombre de parties à charger (défaut: 10)

        Returns:
            dict: Informations sur le chargement
        """
        try:
            with open(chemin_fichier, 'r', encoding='utf-8') as f:
                data = json.load(f)

            # Extraction des métadonnées
            metadata = data.get('metadata', {})
            parties = data.get('parties', [])

            if not parties:
                return {'succes': False, 'erreur': 'Aucune partie trouvée'}

            # Limitation aux N premières parties
            parties_a_traiter = parties[:nb_parties]
            nb_parties_reelles = len(parties_a_traiter)

            # Traitement des parties sélectionnées
            mains_totales = []
            parties_info = []

            for i, partie in enumerate(parties_a_traiter):
                mains = partie.get('mains', [])
                mains_totales.extend(mains)

                # Informations sur chaque partie
                parties_info.append({
                    'numero': i + 1,
                    'mains_total': len(mains),
                    'statistiques': partie.get('statistiques', {})
                })

            # Filtrage des mains valides (exclusion des mains dummy)
            mains_valides = [
                main for main in mains_totales
                if main.get('main_number') is not None and
                   main.get('index1') is not None and
                   main.get('index2') and
                   main.get('index3')
            ]

            # Ajout des observations à l'historique
            observations_ajoutees = 0
            for main in mains_valides:
                index1 = main['index1']
                index2 = main['index2']
                index3 = main['index3']

                # Validation des données
                if (isinstance(index1, int) and index1 in [0, 1] and
                    index2 in ['A', 'B', 'C'] and
                    index3 in ['BANKER', 'PLAYER', 'TIE']):

                    self.ajouter_observation(index1, index2, index3)
                    observations_ajoutees += 1

            return {
                'succes': True,
                'fichier': chemin_fichier,
                'metadata': metadata,
                'nb_parties_demandees': nb_parties,
                'nb_parties_traitees': nb_parties_reelles,
                'parties_info': parties_info,
                'mains_totales': len(mains_totales),
                'mains_dummy': len(mains_totales) - len(mains_valides),
                'mains_valides': len(mains_valides),
                'observations_ajoutees': observations_ajoutees,
                'historique_taille': len(self.historique)
            }

        except FileNotFoundError:
            return {'succes': False, 'erreur': f'Fichier non trouvé: {chemin_fichier}'}
        except json.JSONDecodeError as e:
            return {'succes': False, 'erreur': f'Erreur JSON: {str(e)}'}
        except Exception as e:
            return {'succes': False, 'erreur': f'Erreur inattendue: {str(e)}'}
        
    def ajouter_observation(self, index1, index2, index3):
        """
        Ajoute une nouvelle observation à l'historique

        Args:
            index1: 0 ou 1 (SYNC/DESYNC)
            index2: 'A', 'B', ou 'C'
            index3: 'BANKER', 'PLAYER', ou 'TIE'
        """
        observation = {
            'index1': index1,
            'index2': index2,
            'index3': index3,
            'index5': f"{index1}_{index2}_{index3}"
        }
        self.historique.append(observation)

        # 🔬 NOUVEAU: Mise à jour des probabilités conditionnelles
        self._mettre_a_jour_probabilites_conditionnelles(index1, index2, index3)

        # Invalidation du cache lors de nouvelles observations
        self._cache_hurst.clear()
        self._cache_cycles.clear()

# ============================================================================
# 4. SYSTÈME DE PROBABILITÉS CONDITIONNELLES ADAPTATIVES
# ============================================================================

    def _mettre_a_jour_probabilites_conditionnelles(self, index1, index2, index3):
        """
        Met à jour les compteurs et probabilités conditionnelles
        Implémente le lissage de Laplace et l'adaptation temporelle

        Args:
            index1: INDEX1 observé
            index2: INDEX2 observé
            index3: INDEX3 observé
        """
        # Clé pour la combinaison INDEX1+INDEX2
        cle_condition = f"{index1}_{index2}"

        # Initialisation si première occurrence
        if cle_condition not in self.compteurs_conditionnels:
            self.compteurs_conditionnels[cle_condition] = {
                'PLAYER': 0, 'BANKER': 0, 'TIE': 0, 'total': 0
            }

        # Mise à jour des compteurs
        self.compteurs_conditionnels[cle_condition][index3] += 1
        self.compteurs_conditionnels[cle_condition]['total'] += 1

        # Recalcul des probabilités avec lissage de Laplace
        self._calculer_probabilites_avec_lissage(cle_condition)

        # Calcul des intervalles de confiance
        self._calculer_intervalles_confiance(cle_condition)

    def _calculer_probabilites_avec_lissage(self, cle_condition):
        """
        Calcule les probabilités conditionnelles avec lissage de Laplace
        P(INDEX3|INDEX1,INDEX2) = (count + α) / (total + α*|INDEX3|)

        Args:
            cle_condition: Clé "INDEX1_INDEX2"
        """
        compteurs = self.compteurs_conditionnels[cle_condition]
        alpha = self.parametres_prob['ALPHA_LAPLACE']

        # Nombre total d'observations + lissage
        total_lisse = compteurs['total'] + alpha * 3  # 3 valeurs possibles pour INDEX3

        # Calcul des probabilités lissées
        self.probabilites_conditionnelles[cle_condition] = {
            'P_PLAYER': (compteurs['PLAYER'] + alpha) / total_lisse,
            'P_BANKER': (compteurs['BANKER'] + alpha) / total_lisse,
            'P_TIE': (compteurs['TIE'] + alpha) / total_lisse,
            'total_observations': compteurs['total'],
            'fiabilite': min(1.0, compteurs['total'] / self.parametres_prob['FENETRE_MIN'])
        }

    def _calculer_intervalles_confiance(self, cle_condition):
        """
        Calcule les intervalles de confiance pour les probabilités estimées
        Utilise l'approximation normale pour les proportions

        Args:
            cle_condition: Clé "INDEX1_INDEX2"
        """
        compteurs = self.compteurs_conditionnels[cle_condition]
        n = compteurs['total']

        if n < 10:  # Échantillon trop petit pour intervalles fiables
            self.intervalles_confiance[cle_condition] = {
                'P_PLAYER': (0.0, 1.0), 'P_BANKER': (0.0, 1.0), 'P_TIE': (0.0, 1.0)
            }
            return

        # Niveau de confiance (z-score pour 95%)
        z = 1.96  # Pour 95% de confiance

        intervalles = {}
        for outcome in ['PLAYER', 'BANKER', 'TIE']:
            p = self.probabilites_conditionnelles[cle_condition][f'P_{outcome}']

            # Erreur standard pour proportion
            se = (p * (1 - p) / n) ** 0.5

            # Intervalle de confiance
            marge = z * se
            intervalles[f'P_{outcome}'] = (max(0, p - marge), min(1, p + marge))

        self.intervalles_confiance[cle_condition] = intervalles

# ============================================================================
# 5. MÉTHODES D'ANALYSE FRACTALE
# ============================================================================

    def calculer_exposant_hurst(self, sequence):
        """
        Calcule l'exposant de Hurst pour détecter la persistance/anti-persistance
        
        Args:
            sequence: Séquence numérique à analyser
            
        Returns:
            float: Exposant de Hurst (0.5 = aléatoire, >0.5 = persistant, <0.5 = anti-persistant)
        """
        if len(sequence) < 10:
            return 0.5
            
        # Conversion en array numpy
        ts = np.array(sequence)
        N = len(ts)
        
        # Calcul des échelles
        scales = np.logspace(0.5, np.log10(N//4), 10).astype(int)
        fluctuations = []
        
        for scale in scales:
            # Segmentation
            segments = N // scale
            if segments < 2:
                continue
                
            # Calcul des fluctuations
            fluc = 0
            for i in range(segments):
                segment = ts[i*scale:(i+1)*scale]
                trend = np.polyfit(range(len(segment)), segment, 1)
                detrended = segment - np.polyval(trend, range(len(segment)))
                fluc += np.sqrt(np.mean(detrended**2))
            
            fluctuations.append(fluc / segments)
        
        # Régression log-log pour obtenir H
        if len(fluctuations) > 3:
            log_scales = np.log10(scales[:len(fluctuations)])
            log_flucs = np.log10(fluctuations)
            hurst = np.polyfit(log_scales, log_flucs, 1)[0]
            return max(0.1, min(0.9, hurst))
        
        return 0.5
    
    def analyser_deviations_fractales(self):
        """
        Analyse les déviations par rapport aux proportions fractales de référence
        
        Returns:
            dict: Déviations et forces de rappel vers les attracteurs
        """
        if len(self.historique) < self.fenetre_analyse:
            return {'A': 0, 'B': 0, 'C': 0}
        
        # Extraction des INDEX2 récents
        index2_recent = [obs['index2'] for obs in list(self.historique)[-self.fenetre_analyse:]]
        compteur = Counter(index2_recent)
        
        # Calcul des proportions observées
        total = len(index2_recent)
        proportions_observees = {
            'A': compteur.get('A', 0) / total,
            'B': compteur.get('B', 0) / total,
            'C': compteur.get('C', 0) / total
        }
        
        # Calcul des déviations fractales
        deviations = {}
        for categorie in ['A', 'B', 'C']:
            deviation = proportions_observees[categorie] - self.proportions_fractales[categorie]
            # Force de rappel proportionnelle à la déviation
            force_rappel = -deviation * 2.0  # Coefficient de rappel
            deviations[categorie] = force_rappel
            
        return deviations
    
    def analyser_persistance_index2(self):
        """
        Analyse la persistance des séquences INDEX2 via l'exposant de Hurst

        Returns:
            dict: Exposants de Hurst par catégorie
        """
        # Vérification du cache
        cache_key = f"hurst_{len(self.historique)}"
        if cache_key in self._cache_hurst:
            return self._cache_hurst[cache_key]

        if len(self.historique) < self.fenetre_hurst:
            return {'A': 0.5, 'B': 0.5, 'C': 0.5}

        # Conversion INDEX2 en séquences numériques
        sequence_recent = [obs['index2'] for obs in list(self.historique)[-self.fenetre_hurst:]]

        # Séquences binaires pour chaque catégorie
        seq_A = [1 if x == 'A' else 0 for x in sequence_recent]
        seq_B = [1 if x == 'B' else 0 for x in sequence_recent]
        seq_C = [1 if x == 'C' else 0 for x in sequence_recent]

        result = {
            'A': self.calculer_exposant_hurst(seq_A),
            'B': self.calculer_exposant_hurst(seq_B),
            'C': self.calculer_exposant_hurst(seq_C)
        }

        # Mise en cache
        self._cache_hurst[cache_key] = result
        return result
    
    def calculer_index1_bct(self, index1_actuel, index2_actuel):
        """
        CALCUL DÉTERMINISTE d'INDEX1 selon les règles BCT
        Ce n'est PAS une prédiction mais un calcul mécanique et automatique

        Args:
            index1_actuel: INDEX1 à la main n
            index2_actuel: INDEX2 à la main n

        Returns:
            int: INDEX1 pour la main n+1 (100% déterministe)

        Règles BCT (Business Card Theory) - MÉCANIQUES :
        - Si INDEX2=C : Alternance → INDEX1 suivant = 1-INDEX1_actuel
        - Si INDEX2=A : Conservation → INDEX1 suivant = INDEX1_actuel
        - Si INDEX2=B : Conservation → INDEX1 suivant = INDEX1_actuel
        """
        if index2_actuel == 'C':
            # RÈGLE C: Alternance forcée (mécanique)
            return 1 - index1_actuel
        else:
            # RÈGLES A et B: Conservation (mécanique)
            return index1_actuel
    
    def predire_index2_fractal(self):
        """
        Prédiction d'INDEX2 basée sur l'analyse fractale
        
        Returns:
            str: INDEX2 prédit ('A', 'B', ou 'C')
        """
        # Analyse des déviations fractales
        deviations = self.analyser_deviations_fractales()
        
        # Analyse de la persistance
        persistance = self.analyser_persistance_index2()
        
        # Scores de prédiction combinés
        scores = {}
        for categorie in ['A', 'B', 'C']:
            # Force de rappel fractal
            score_rappel = deviations[categorie]
            
            # Correction de persistance
            if persistance[categorie] > 0.6:
                # Persistance élevée → continuer la tendance
                score_persistance = 0.2
            elif persistance[categorie] < 0.4:
                # Anti-persistance → retour vers la moyenne
                score_persistance = -0.2
            else:
                score_persistance = 0
            
            scores[categorie] = score_rappel + score_persistance
        
        # Sélection de la catégorie avec le score maximal
        categorie_predite = max(scores.keys(), key=lambda k: scores[k])
        
        return categorie_predite
    
    def predire_index3_probabiliste(self, index1_suivant, index2_predit):
        """
        🔬 NOUVELLE MÉTHODE: Prédiction d'INDEX3 basée sur les probabilités conditionnelles

        Args:
            index1_suivant: INDEX1 calculé pour la main n+1
            index2_predit: INDEX2 prédit pour la main n+1

        Returns:
            dict: Prédiction avec probabilités et confiance
        """
        cle_condition = f"{index1_suivant}_{index2_predit}"

        # Vérifier si nous avons des données pour cette combinaison
        if cle_condition in self.probabilites_conditionnelles:
            probs = self.probabilites_conditionnelles[cle_condition]

            # Sélection basée sur la probabilité maximale (PLAYER vs BANKER uniquement)
            p_player = probs['P_PLAYER']
            p_banker = probs['P_BANKER']

            if p_player > p_banker:
                prediction = 'PLAYER'
                confiance_prob = p_player / (p_player + p_banker)  # Normalisation sans TIE
            else:
                prediction = 'BANKER'
                confiance_prob = p_banker / (p_player + p_banker)

            # Ajustement de confiance basé sur la fiabilité des données
            confiance_finale = confiance_prob * probs['fiabilite']

            return {
                'index3_predit': prediction,
                'confiance_probabiliste': confiance_finale,
                'p_player': p_player,
                'p_banker': p_banker,
                'p_tie': probs['P_TIE'],
                'observations_utilisees': probs['total_observations'],
                'methode': 'PROBABILISTE_CONDITIONNEL'
            }
        else:
            # Fallback vers méthode des biais structurels
            return self._predire_index3_biais_fallback(index2_predit)

    def _predire_index3_biais_fallback(self, index2_predit):
        """
        Méthode de fallback basée sur les biais structurels (ancienne méthode)
        """
        if index2_predit == 'A':
            # Zone d'équilibre → alternance
            if len(self.historique) > 0:
                dernier_index3 = self.historique[-1]['index3']
                prediction = 'PLAYER' if dernier_index3 == 'BANKER' else 'BANKER'
            else:
                prediction = 'BANKER'
            confiance = 0.5

        elif index2_predit == 'B':
            # Avantage PLAYER (+7.9%)
            prediction = 'PLAYER'
            confiance = 0.579  # 50% + 7.9%

        elif index2_predit == 'C':
            # Avantage BANKER (+12.1%)
            prediction = 'BANKER'
            confiance = 0.621  # 50% + 12.1%
        else:
            prediction = 'BANKER'
            confiance = 0.5

        return {
            'index3_predit': prediction,
            'confiance_probabiliste': confiance,
            'p_player': 0.5,
            'p_banker': 0.5,
            'p_tie': 0.0,
            'observations_utilisees': 0,
            'methode': 'BIAIS_STRUCTUREL_FALLBACK'
        }
    
    def calculer_confiance_prediction(self, index2_predit):
        """
        Calcule le niveau de confiance de la prédiction
        
        Args:
            index2_predit: INDEX2 prédit
            
        Returns:
            float: Niveau de confiance (0.0 à 1.0)
        """
        if len(self.historique) < self.fenetre_analyse:
            return 0.5
        
        # Facteurs de confiance
        confiance_base = 0.5
        
        # Confiance basée sur les biais structurels
        if index2_predit == 'B':
            confiance_biais = 0.579  # 50% + 7.9%
        elif index2_predit == 'C':
            confiance_biais = 0.621  # 50% + 12.1%
        else:  # A
            confiance_biais = 0.500  # Équilibre
        
        # Confiance basée sur la stabilité des proportions
        deviations = self.analyser_deviations_fractales()
        stabilite = 1.0 - abs(deviations[index2_predit])
        confiance_stabilite = max(0.3, min(0.9, stabilite))
        
        # Confiance combinée
        confiance_finale = (confiance_biais * 0.7 + confiance_stabilite * 0.3)
        
        return max(0.3, min(0.95, confiance_finale))
    
    def predire_main_suivante(self):
        """
        Prédiction complète pour la main n+1 avec fusion fractale optimisée

        Returns:
            dict: Prédiction complète avec niveaux de confiance
        """
        if len(self.historique) == 0:
            return {
                'index1_suivant': 0,  # CALCULÉ (pas prédit)
                'index2_predit': 'A',
                'index3_predit': 'BANKER',
                'index5_predit': '0_A_BANKER',
                'confiance': 0.5,
                'methode': 'INITIALISATION'
            }

        # Récupération de l'état actuel
        etat_actuel = self.historique[-1]
        index1_actuel = etat_actuel['index1']
        index2_actuel = etat_actuel['index2']

        # ÉTAPE 1: CALCUL INDEX1 (100% déterministe via règles BCT)
        index1_suivant = self.calculer_index1_bct(index1_actuel, index2_actuel)

        # ÉTAPE 2: Prédiction INDEX2 (fusion fractale optimisée)
        fusion_result = self.fusion_predictions_fractales()
        index2_predit = fusion_result['index2_predit']
        confiance_fusion = fusion_result['confiance_fusion']

        # ÉTAPE 3: Prédiction INDEX3 (probabilités conditionnelles)
        resultat_index3 = self.predire_index3_probabiliste(index1_suivant, index2_predit)
        index3_predit = resultat_index3['index3_predit']

        # ÉTAPE 4: Calcul de la confiance finale (fusion probabiliste + fractale)
        confiance_fractale = self.calculer_confiance_prediction(index2_predit)
        confiance_probabiliste = resultat_index3['confiance_probabiliste']

        # Fusion des confiances (pondération adaptative)
        if resultat_index3['observations_utilisees'] >= self.parametres_prob['FENETRE_MIN']:
            # Données suffisantes → privilégier approche probabiliste
            poids_prob = 0.7
            poids_fractal = 0.3
        else:
            # Données insuffisantes → privilégier approche fractale
            poids_prob = 0.3
            poids_fractal = 0.7

        confiance_base = (confiance_probabiliste * poids_prob +
                         confiance_fractale * poids_fractal)
        confiance_finale = (confiance_base * 0.6 + confiance_fusion * 0.4)

        # Bonus de confiance pour les cycles détectés
        cycles = self.detecter_cycles_fractals()
        if cycles['cycle_detecte'] and cycles['confiance'] > 0.7:
            confiance_finale = min(0.95, confiance_finale + 0.1)

        # Construction de la prédiction complète
        index5_predit = f"{index1_suivant}_{index2_predit}_{index3_predit}"

        prediction = {
            'index1_suivant': index1_suivant,  # CALCULÉ (pas prédit)
            'index2_predit': index2_predit,
            'index3_predit': index3_predit,
            'index5_predit': index5_predit,
            'confiance': confiance_finale,
            'confiance_fusion': confiance_fusion,
            'methodes_fusion': fusion_result['methodes_utilisees'],
            'cycle_detecte': cycles['cycle_detecte'],
            'methode': 'FUSION_FRACTALE_PROBABILISTE',
            # 🔬 NOUVELLES MÉTRIQUES PROBABILISTES
            'probabilites_conditionnelles': {
                'p_player': resultat_index3['p_player'],
                'p_banker': resultat_index3['p_banker'],
                'p_tie': resultat_index3['p_tie']
            },
            'observations_utilisees': resultat_index3['observations_utilisees'],
            'methode_index3': resultat_index3['methode'],
            'confiance_probabiliste': confiance_probabiliste,
            'confiance_fractale': confiance_fractale,
            'poids_probabiliste': poids_prob,
            'poids_fractal': poids_fractal
        }

        # Sauvegarde pour évaluation
        self.predictions_historique.append(prediction)

        return prediction
    
    def evaluer_prediction(self, index3_reel):
        """
        Évalue la performance de la dernière prédiction

        Args:
            index3_reel: Résultat réel de la main

        Note: Si TIE survient, ce n'est ni un succès ni un échec.
              Seules les prédictions PLAYER vs BANKER sont évaluées.
        """
        if self.predictions_historique:
            derniere_prediction = self.predictions_historique[-1]
            index3_predit = derniere_prediction['index3_predit']

            # Si le résultat réel est TIE, on ignore cette prédiction
            # TIE n'est jamais prédit et ne compte pas comme échec
            if index3_reel == 'TIE':
                return  # Pas d'évaluation pour les TIE

            # Évaluation uniquement pour PLAYER vs BANKER
            self.performance['total'] += 1
            if index3_predit == index3_reel:
                self.performance['correct'] += 1
    
    def obtenir_performance(self):
        """
        Retourne les statistiques de performance

        Returns:
            dict: Statistiques de performance
        """
        if self.performance['total'] == 0:
            return {'taux_reussite': 0.0, 'predictions_total': 0}

        taux = self.performance['correct'] / self.performance['total']
        return {
            'taux_reussite': taux,
            'predictions_correctes': self.performance['correct'],
            'predictions_total': self.performance['total']
        }

    def analyser_entropie_sequence(self, sequence, m=2):
        """
        Calcule l'entropie approximative d'une séquence (version simplifiée pour INDEX2)

        Args:
            sequence: Séquence à analyser (INDEX2: A, B, C)
            m: Longueur des patterns

        Returns:
            float: Entropie approximative
        """
        if len(sequence) < m + 1:
            return 1.0

        # Conversion des lettres en nombres pour le calcul
        mapping = {'A': 0, 'B': 1, 'C': 2}
        sequence_num = [mapping.get(x, 0) for x in sequence]

        def _maxdist(xi, xj, m):
            return max([abs(ua - va) for ua, va in zip(xi, xj)])

        def _phi(m):
            patterns = [sequence_num[i:i+m] for i in range(len(sequence_num) - m + 1)]
            if not patterns:
                return 0

            C = np.zeros(len(patterns))

            for i in range(len(patterns)):
                template_i = patterns[i]
                for j in range(len(patterns)):
                    if _maxdist(template_i, patterns[j], m) <= 0.5:
                        C[i] += 1.0

            # Éviter log(0)
            C = np.maximum(C, 1e-10)
            phi = np.mean(np.log(C / len(patterns)))
            return phi

        try:
            return _phi(m) - _phi(m + 1)
        except:
            # Fallback: entropie de Shannon simple
            compteur = Counter(sequence)
            total = len(sequence)
            entropie = -sum((count/total) * np.log2(count/total) for count in compteur.values())
            return entropie / np.log2(3)  # Normalisation pour 3 catégories

    def detecter_cycles_fractals(self):
        """
        Détecte les cycles fractals dans les séquences INDEX2

        Returns:
            dict: Information sur les cycles détectés
        """
        # Vérification du cache
        cache_key = f"cycles_{len(self.historique)}"
        if cache_key in self._cache_cycles:
            return self._cache_cycles[cache_key]

        if len(self.historique) < 20:
            return {'cycle_detecte': False, 'longueur': 0, 'confiance': 0.0}

        # Extraction de la séquence INDEX2
        sequence_index2 = [obs['index2'] for obs in list(self.historique)[-50:]]

        # Recherche de cycles de différentes longueurs
        for longueur_cycle in range(2, 10):
            if len(sequence_index2) < longueur_cycle * 3:
                continue

            # Vérification de répétition
            cycles_detectes = 0
            for i in range(len(sequence_index2) - longueur_cycle * 2):
                cycle1 = sequence_index2[i:i+longueur_cycle]
                cycle2 = sequence_index2[i+longueur_cycle:i+longueur_cycle*2]

                if cycle1 == cycle2:
                    cycles_detectes += 1

            # Si plus de 2 cycles identiques détectés
            if cycles_detectes >= 2:
                confiance_cycle = min(0.9, cycles_detectes / 5.0)
                result = {
                    'cycle_detecte': True,
                    'longueur': longueur_cycle,
                    'confiance': confiance_cycle,
                    'pattern': sequence_index2[-longueur_cycle:]
                }
                # Mise en cache
                self._cache_cycles[cache_key] = result
                return result

        result = {'cycle_detecte': False, 'longueur': 0, 'confiance': 0.0}
        self._cache_cycles[cache_key] = result
        return result

# ============================================================================
# 5. MÉTHODES DE PRÉDICTION SPÉCIALISÉES
# ============================================================================

    def predire_avec_cycles(self):
        """
        Prédiction basée sur la détection de cycles fractals

        Returns:
            str or None: INDEX2 prédit si cycle détecté, None sinon
        """
        cycles = self.detecter_cycles_fractals()

        if cycles['cycle_detecte'] and cycles['confiance'] > 0.6:
            pattern = cycles['pattern']
            sequence_actuelle = [obs['index2'] for obs in list(self.historique)[-len(pattern):]]

            # Vérification si on est dans le pattern
            if sequence_actuelle == pattern:
                # Prédiction du prochain élément du cycle
                position_dans_cycle = len(sequence_actuelle) % cycles['longueur']
                if position_dans_cycle < len(pattern):
                    return pattern[position_dans_cycle]

        return None

    def fusion_predictions_fractales(self):
        """
        Fusion de toutes les méthodes fractales pour une prédiction optimale

        Returns:
            dict: Prédiction fusionnée avec scores de confiance
        """
        # Méthode 1: Analyse fractale standard
        index2_fractal = self.predire_index2_fractal()

        # Méthode 2: Détection de cycles
        index2_cycle = self.predire_avec_cycles()

        # Méthode 3: Analyse entropique
        if len(self.historique) >= 20:
            sequence_recent = [obs['index2'] for obs in list(self.historique)[-20:]]
            entropie = self.analyser_entropie_sequence(sequence_recent)

            # Faible entropie = haute prédictibilité
            if entropie < 0.5:
                # Prédire la continuation du pattern dominant
                compteur = Counter(sequence_recent[-5:])
                index2_entropie = compteur.most_common(1)[0][0]
            else:
                index2_entropie = None
        else:
            index2_entropie = None

        # Fusion des prédictions
        predictions = [index2_fractal]
        poids = [0.4]  # Poids de base pour la méthode fractale

        if index2_cycle:
            predictions.append(index2_cycle)
            poids.append(0.4)  # Poids élevé pour les cycles détectés

        if index2_entropie:
            predictions.append(index2_entropie)
            poids.append(0.2)  # Poids modéré pour l'entropie

        # Normalisation des poids
        poids_total = sum(poids)
        poids = [p / poids_total for p in poids]

        # Vote pondéré
        votes = Counter()
        for pred, poids_pred in zip(predictions, poids):
            votes[pred] += poids_pred

        # Sélection de la prédiction avec le score le plus élevé
        index2_final = votes.most_common(1)[0][0]
        confiance_fusion = votes[index2_final]

        return {
            'index2_predit': index2_final,
            'confiance_fusion': confiance_fusion,
            'methodes_utilisees': len(predictions)
        }

# ============================================================================
# 6. MÉTHODES D'ÉVALUATION ET PERFORMANCE
# ============================================================================

    def generer_rapport_analyse(self):
        """
        Génère un rapport détaillé de l'analyse fractale

        Returns:
            str: Rapport formaté
        """
        if len(self.historique) < 10:
            return "Historique insuffisant pour l'analyse"

        # Analyse des proportions
        deviations = self.analyser_deviations_fractales()
        persistance = self.analyser_persistance_index2()
        cycles = self.detecter_cycles_fractals()
        performance = self.obtenir_performance()

        rapport = []
        rapport.append("🌀 RAPPORT D'ANALYSE FRACTALE INDEX5")
        rapport.append("=" * 50)
        rapport.append(f"Observations analysées: {len(self.historique)}")
        rapport.append("")

        rapport.append("📊 DÉVIATIONS FRACTALES:")
        for cat in ['A', 'B', 'C']:
            prop_ref = self.proportions_fractales[cat]
            deviation = deviations[cat]
            rapport.append(f"  {cat}: {prop_ref:.1%} (référence) | Déviation: {deviation:+.3f}")
        rapport.append("")

        rapport.append("🔄 PERSISTANCE (Exposant de Hurst):")
        for cat in ['A', 'B', 'C']:
            h = persistance[cat]
            if h > 0.6:
                tendance = "PERSISTANT"
            elif h < 0.4:
                tendance = "ANTI-PERSISTANT"
            else:
                tendance = "NEUTRE"
            rapport.append(f"  {cat}: H={h:.3f} ({tendance})")
        rapport.append("")

        # 🔬 NOUVEAU: Rapport des probabilités conditionnelles
        rapport.append("🎲 PROBABILITÉS CONDITIONNELLES P(INDEX3|INDEX1,INDEX2):")
        rapport.append("-" * 60)

        if self.probabilites_conditionnelles:
            for cle_condition, probs in self.probabilites_conditionnelles.items():
                index1, index2 = cle_condition.split('_')
                rapport.append(f"  📍 INDEX1={index1}, INDEX2={index2} ({probs['total_observations']} obs.):")
                rapport.append(f"    P(PLAYER) = {probs['P_PLAYER']:.3f}")
                rapport.append(f"    P(BANKER) = {probs['P_BANKER']:.3f}")
                rapport.append(f"    P(TIE)    = {probs['P_TIE']:.3f}")
                rapport.append(f"    Fiabilité = {probs['fiabilite']:.1%}")

                # Intervalles de confiance si disponibles
                if cle_condition in self.intervalles_confiance:
                    ic = self.intervalles_confiance[cle_condition]
                    rapport.append(f"    IC 95% PLAYER: [{ic['P_PLAYER'][0]:.3f}, {ic['P_PLAYER'][1]:.3f}]")
                    rapport.append(f"    IC 95% BANKER: [{ic['P_BANKER'][0]:.3f}, {ic['P_BANKER'][1]:.3f}]")
                rapport.append("")
        else:
            rapport.append("  Aucune donnée de probabilités conditionnelles disponible")
            rapport.append("")

        rapport.append("🔁 CYCLES FRACTALS:")
        if cycles['cycle_detecte']:
            rapport.append(f"  Cycle détecté: Longueur {cycles['longueur']}")
            rapport.append(f"  Confiance: {cycles['confiance']:.1%}")
            rapport.append(f"  Pattern: {cycles.get('pattern', 'N/A')}")
        else:
            rapport.append("  Aucun cycle significatif détecté")
        rapport.append("")

        rapport.append("🎯 PERFORMANCE:")
        if performance['predictions_total'] > 0:
            rapport.append(f"  Taux de réussite: {performance['taux_reussite']:.1%}")
            rapport.append(f"  Prédictions: {performance['predictions_correctes']}/{performance['predictions_total']}")
        else:
            rapport.append("  Aucune prédiction évaluée")

        return "\n".join(rapport)

# ============================================================================
# 7. MÉTHODES DE RAPPORT ET VISUALISATION
# ============================================================================
# (Les méthodes de rapport sont intégrées dans la classe ci-dessus)

# ============================================================================
# 8. EXEMPLE D'UTILISATION ET TESTS
# ============================================================================

# Exemple d'utilisation et test complet
if __name__ == "__main__":
    print("🚀 INITIALISATION DU PRÉDICTEUR FRACTAL INDEX5")
    print("=" * 60)

    # Initialisation du prédicteur
    predicteur = PredicteurFractalIndex5(fenetre_analyse=50, fenetre_hurst=30)

    # Test avec le dataset JSON (10 premières parties)
    print("📂 CHARGEMENT DU DATASET JSON (10 PREMIÈRES PARTIES)...")
    dataset_path = "dataset_baccarat_lupasco_20250629_165801.json"

    # Chargement des 10 premières parties du dataset
    resultat_chargement = predicteur.charger_dataset_json(dataset_path, nb_parties=10)

    if resultat_chargement['succes']:
        print("✅ DATASET CHARGÉ AVEC SUCCÈS !")
        print(f"📊 Fichier: {resultat_chargement['fichier']}")
        print(f"🎯 Parties demandées: {resultat_chargement['nb_parties_demandees']}")
        print(f"🎯 Parties traitées: {resultat_chargement['nb_parties_traitees']}")
        print(f"📈 Mains totales: {resultat_chargement['mains_totales']}")
        print(f"🚫 Mains dummy: {resultat_chargement['mains_dummy']}")
        print(f"✅ Mains valides: {resultat_chargement['mains_valides']}")
        print(f"💾 Observations ajoutées: {resultat_chargement['observations_ajoutees']}")
        print(f"🔄 Taille historique: {resultat_chargement['historique_taille']}")
        print("")

        # Détail des parties traitées
        print("📋 DÉTAIL DES PARTIES TRAITÉES:")
        for partie_info in resultat_chargement['parties_info']:
            stats = partie_info['statistiques']
            print(f"  Partie {partie_info['numero']}: {partie_info['mains_total']} mains | "
                  f"P/B: {stats.get('total_manches_pb', 'N/A')} | "
                  f"TIE: {stats.get('total_ties', 'N/A')}")
        print("")

        # Métadonnées du générateur
        metadata = resultat_chargement['metadata']
        print("🔧 MÉTADONNÉES DU GÉNÉRATEUR:")
        print(f"  Générateur: {metadata.get('generateur', 'N/A')}")
        print(f"  Version: {metadata.get('version', 'N/A')}")
        print(f"  Date: {metadata.get('date_generation', 'N/A')}")
        print(f"  Hasard crypto: {metadata.get('hasard_cryptographique', 'N/A')}")
        print("")

        # Test de prédictions sur les données réelles
        print("🎯 TEST DE PRÉDICTIONS SUR DONNÉES RÉELLES")
        print("=" * 50)

        # Simulation de prédictions en temps réel
        historique_complet = list(predicteur.historique)
        predicteur.historique.clear()  # Reset pour simulation
        predicteur.performance = {'correct': 0, 'total': 0}

        # Ajout progressif avec prédictions
        for i, observation in enumerate(historique_complet):
            # Prédiction avant d'ajouter l'observation (sauf pour les premières)
            if i >= 10:  # Commencer les prédictions après 10 observations
                prediction = predicteur.predire_main_suivante()
                # Évaluation de la prédiction
                predicteur.evaluer_prediction(observation['index3'])

                if i % 15 == 0:  # Affichage périodique
                    print(f"Main {i}: Prédit {prediction['index3_predit']} | Réel {observation['index3']} | Confiance {prediction['confiance']:.1%}")

            # Ajout de l'observation réelle
            predicteur.ajouter_observation(
                observation['index1'],
                observation['index2'],
                observation['index3']
            )

        print("")
        print("🎯 PRÉDICTION FINALE POUR LA MAIN SUIVANTE")
        print("=" * 50)

        # Prédiction finale
        prediction_finale = predicteur.predire_main_suivante()

        print(f"INDEX1 calculé: {prediction_finale['index1_suivant']} (déterministe BCT)")
        print(f"INDEX2 prédit: {prediction_finale['index2_predit']}")
        print(f"INDEX3 prédit: {prediction_finale['index3_predit']}")
        print(f"INDEX5 prédit: {prediction_finale['index5_predit']}")
        print(f"Confiance: {prediction_finale['confiance']:.1%}")
        print(f"Méthodes fusion: {prediction_finale['methodes_fusion']}")
        print(f"Cycle détecté: {prediction_finale['cycle_detecte']}")
        print(f"Méthode: {prediction_finale['methode']}")

        print("")
        print(predicteur.generer_rapport_analyse())

    else:
        print("❌ ERREUR LORS DU CHARGEMENT DU DATASET")
        print(f"Erreur: {resultat_chargement['erreur']}")
        print("")
        print("🔄 UTILISATION DU DATASET SIMULÉ...")

        # Fallback vers le dataset simulé original

        # Simulation d'un historique plus réaliste basé sur les données réelles
        historique_realiste = [
            # Séquence basée sur les proportions fractales observées
            (0, 'A', 'BANKER'), (0, 'A', 'PLAYER'), (0, 'B', 'PLAYER'),
            (0, 'C', 'BANKER'), (1, 'C', 'PLAYER'), (0, 'A', 'BANKER'),
            (0, 'B', 'PLAYER'), (0, 'C', 'BANKER'), (1, 'A', 'PLAYER'),
            (1, 'B', 'PLAYER'), (1, 'C', 'BANKER'), (0, 'A', 'PLAYER'),
            (0, 'C', 'BANKER'), (1, 'B', 'PLAYER'), (1, 'A', 'BANKER'),
            (1, 'C', 'PLAYER'), (0, 'B', 'PLAYER'), (0, 'A', 'BANKER'),
            (0, 'C', 'BANKER'), (1, 'A', 'PLAYER'), (1, 'B', 'PLAYER'),
            (1, 'C', 'BANKER'), (0, 'A', 'PLAYER'), (0, 'B', 'PLAYER'),
            (0, 'C', 'BANKER'), (1, 'A', 'BANKER'), (1, 'B', 'PLAYER'),
            (1, 'C', 'PLAYER'), (0, 'A', 'BANKER'), (0, 'B', 'PLAYER')
        ]

        print(f"📥 Ajout de {len(historique_realiste)} observations...")

        # Ajout des observations avec simulation de prédictions
        for i, (index1, index2, index3) in enumerate(historique_realiste):
            # Prédiction avant d'ajouter l'observation (sauf pour la première)
            if i > 0:
                prediction = predicteur.predire_main_suivante()
                # Évaluation de la prédiction
                predicteur.evaluer_prediction(index3)

                if i % 10 == 0:  # Affichage périodique
                    print(f"Main {i}: Prédit {prediction['index3_predit']} | Réel {index3} | Confiance {prediction['confiance']:.1%}")

            # Ajout de l'observation réelle
            predicteur.ajouter_observation(index1, index2, index3)

        print("\n🎯 PRÉDICTION FINALE POUR LA MAIN SUIVANTE")
        print("=" * 50)

        # Prédiction finale
        prediction_finale = predicteur.predire_main_suivante()

        print(f"INDEX1 calculé: {prediction_finale['index1_suivant']} (déterministe BCT)")
        print(f"INDEX2 prédit: {prediction_finale['index2_predit']}")
        print(f"INDEX3 prédit: {prediction_finale['index3_predit']}")
        print(f"INDEX5 prédit: {prediction_finale['index5_predit']}")
        print(f"Confiance: {prediction_finale['confiance']:.1%}")
        print(f"Méthodes fusion: {prediction_finale['methodes_fusion']}")
        print(f"Cycle détecté: {prediction_finale['cycle_detecte']}")
        print(f"Méthode: {prediction_finale['methode']}")

        print("\n" + predicteur.generer_rapport_analyse())
